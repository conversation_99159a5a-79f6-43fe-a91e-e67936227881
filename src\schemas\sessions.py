"""
Pydantic schemas for session-related operations.
"""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime


class ChatRequest(BaseModel):
    """
    Schema for chat request.
    """
    message: str
    image_path: Optional[str] = ""
    session_id: Optional[str] = ""
    part_file_name: str = "part_file_name"  # Merged from app/schemas.py - default value
    export_format: str = "obj"  # obj, step, dxf
    material_choice: str = "STEEL"  # STEEL, STAINLESS, ALUMINUM - Merged from app/schemas.py
    selected_feature_uuid: Optional[str] = ""


class ChatResponse(BaseModel):
    """
    Schema for chat response.

    The chat_response field contains the chatbot's response message but does not include
    the generated code. It may contain:
    - Success messages when the model is created successfully
    - Error messages if something went wrong
    - Requests for additional information if parameters are missing
    - Other informational messages from the chatbot

    The actual generated code is not included in the response.
    """
    chat_response: str  # Contains the chatbot's response, but not the actual code
    session_id: str
    obj_export: Optional[str] = None
    tessellated_export: Optional[Dict[str, Any]] = None
    attribute_and_transientid_map: Optional[Dict[str, Any]] = None
    manufacturing_errors: Optional[List[str]] = None


class SessionInfo(BaseModel):
    """
    Schema for detailed session information.
    """
    id: int
    session_id: str
    name: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # For Pydantic v2, replaces orm_mode
