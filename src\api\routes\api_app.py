"""
API app for routes in cad.py and sessions.py.
This creates a separate FastAPI app instance for these routes.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

# Set up logging
logger = logging.getLogger("tolery-api-routes")

# Create a separate FastAPI app for the routes
api_app = FastAPI(
    title="Tolery API Production",
    description="Production API routes for CAD and session operations",
    version="0.2.0",
    # Configure docs URL to be at the root of the mounted path
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
api_app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import the routers
try:
    from .sessions import router as sessions_router
    from .cad import router as cad_router
except ImportError:
    # Fallback for direct imports
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.api.routes.sessions import router as sessions_router
    from src.api.routes.cad import router as cad_router

# Include the routers in the api_app
api_app.include_router(sessions_router)
api_app.include_router(cad_router)

logger.info("API routes app initialized with sessions and CAD routers")
