#!/usr/bin/env python
"""
DFM Shape ChatBot - Main entry point

This script starts the FastAPI server for the DFM Shape ChatBot application.
"""
import os
import sys
import logging
import uvicorn
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("server.log")
    ]
)
logger = logging.getLogger("dfm-shapechatbot")

def main():
    """
    Main entry point for the application.
    Starts the FastAPI server with the specified configuration.
    """
    try:
        # Get port from environment variable or use default
        port = int(os.getenv('UVICORN_PORT', 8080))

        # Log startup information
        logger.info(f"Starting DFM Shape ChatBot server on port {port}")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Working directory: {Path.cwd()}")

        # Log database connection info
        db_host = os.getenv('MYSQL_HOST', 'localhost')
        db_port = os.getenv('MYSQL_PORT', '3306')
        db_user = os.getenv('MYSQL_USER', 'root')
        db_name = os.getenv('MYSQL_DATABASE', 'local')
        logger.info(f"Database configuration: {db_user}@{db_host}:{db_port}/{db_name}")

        # Start the server
        uvicorn.run(
            "src.api.main:app",
            host="0.0.0.0",
            port=port,
            reload=False,  # Set to True for development
            log_level="info"
        )
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()